import { Component } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { MaterialPackage } from '@app-his/utils';
import { MatDialog } from '@angular/material/dialog';
import { OperatorCntrService,HisWebapiModelCounterEditCounterSessionModel } from 'ecmed-api/billing';
import {
    OpenCounterPopUpComponent, CloseCounterPopUpComponent, PrintPopUpComponent, SessionPopUpComponent,
    CloseSessionPopUpComponent, ConfirmSessionActualsComponent
} from '@app-his/components';
import { FillHeightDirective } from 'ec-ngcore/ui';

@Component({
    standalone: true,
    imports: [CommonModule, MaterialPackage, DatePipe,FillHeightDirective],
    templateUrl: './view-counter.component.html',
    styleUrl: './view-counter.component.scss',
})
export class ViewCounterComponent {
    counterButton = 'Open Counter';
    counterStatus = 'Close';
    sessionButton = 'Open Session';
    sessionStatus = 'Close';
    disableCounter: Boolean = false;
    disableSession: Boolean = true;
    disableConfirmActual: Boolean = true;
    sessionCount = 0;
    todayDate?:Date;
    floatAmount = 0;
    openingAmount = 0;
    endTime?:string;
    startTime?:string;
    confirmActual = false;
    counterDes:any;
    counterDetails:any;
    loginCount = 0
    openSessionDetails:any;
    sessionInfo?: Data;

    constructor(
        public _dialog: MatDialog,
        public _API: OperatorCntrService) { }

    ngOnInit() {
        let toDate = new Date();
        let endDate = `${toDate.getFullYear()} / ${toDate.getMonth() + 1
            } / ${toDate.getDate()}`;
    }

    public openCounter() {
        if (this.counterButton === 'Open Counter') {

            this.confirmActual = false;

            /* FIXME WHAT IS THIS HARDCODING. Need to store the counter details part of the user session */
            this._API.operatorCntrGetCounterDetailsGet({code:"APPT1"}).subscribe((result) => {
                this.counterDetails = result
                this.counterDes = this.counterDetails[0].DESCRIPTION;
                this.todayDate = this.counterDetails[0].CREATED_DATE;
                this.loginCount++;
            })

            let Date1 = new Date(this.counterDetails[0].CREATED_DATE);
            let counterDate = `${Date1.getFullYear()}-0${Date1.getMonth() + 1
                }-${Date1.getDate()}`;

            let openSessionDetails: HisWebapiModelCounterEditCounterSessionModel = {
                Identifier: "",
                Countersid: this.counterDetails[0].IDENTIFIER.toString(),
                CreatedBy: "",
                CreatedDate: new Date().toISOString(),
                ModifiedBy: "",
                ModifiedDate: new Date().toISOString(),
                ClosedDate: "",
                ClosedBy: "",
                ReconciledBy: "",
                ReconciledDate: "",
                ReconcilationRemarks: "",
                DBOperationFlag: 0
            }

            this._API.operatorCntrOpenSessionPost({hisWebapiModelCounterEditCounterSessionModel:openSessionDetails}).subscribe((result) => {

            })
            const _dialogRef = this._dialog.open(OpenCounterPopUpComponent, {
                width: '40%',
                height: 'fit-content',
                disableClose: true,
            });
            _dialogRef.afterClosed().subscribe((item) => {
                if (item.data != undefined) {
                    this.floatAmount = item.data;
                }
                this.counterButton = 'Close Counter';
                this.disableSession = false;
                this.counterStatus = 'Open';
                this.sessionButton = 'Close Session';
                this.disableCounter = true;
                this.sessionStatus = 'Open';
            });
            let toDate = new Date();
            let time = `${toDate.getHours()} : ${toDate.getMinutes()} : ${toDate.getSeconds()}`;
            this.startTime = time;
            this.endTime = '';
            this.sessionCount = this.sessionCount + 1;
        } else {
            const _dialogRef = this._dialog.open(CloseCounterPopUpComponent, {
                width: '50%',
                height: 'fit-content',
                disableClose: true,
                data: this.confirmActual
            });
            _dialogRef.afterClosed().subscribe((item) => {

                if (item.data == 1) {
                    this.confirmActuals();
                }

                if (item.data != undefined) {
                    this.counterButton = 'Open Counter';
                    this.disableSession = true;
                    this.counterStatus = 'Close';
                }

                this.disableConfirmActual = true;
            });
        }
    }

    public print() {
        this._dialog.open(PrintPopUpComponent, {
            width: '60%',
            height: 'fit-content',
            disableClose: true,
        });
    }

    public session() {
        if (this.sessionButton === 'Open Session') {
            const _dialogRef = this._dialog.open(SessionPopUpComponent, {
                width: '40%',
                height: 'fit-content',
                disableClose: true,
            });
            _dialogRef.afterClosed().subscribe((item) => {
                if (item.data != undefined) {
                    this.openingAmount = item.data;
                }
                this.sessionButton = 'Close Session';
                this.disableCounter = true;
                this.sessionStatus = 'Open';
            });
            let toDate = new Date();
            let time = `${toDate.getHours()} : ${toDate.getMinutes()} : ${toDate.getSeconds()}`;
            this.startTime = time;
            this.endTime = '';
            this.sessionCount = this.sessionCount + 1;
        } else {
            const _dialogRef = this._dialog.open(CloseSessionPopUpComponent, {
                width: '40%',
                height: 'fit-content',
                disableClose: true,
            });
            _dialogRef.afterClosed().subscribe((item) => {
                this.sessionButton = 'Open Session';
                this.disableCounter = false;
                this.sessionStatus = 'Close';

                if (this.sessionCount > 0 && this.sessionStatus === 'Close') {
                    this.disableConfirmActual = false;
                }

                let toDate = new Date();
                let time = `${toDate.getHours()} : ${toDate.getMinutes()} : ${toDate.getSeconds()}`;
                this.endTime = time;

                let sessioInfo: Data = {
                    LOGIN: this.loginCount.toString(),
                    SESSION: this.sessionCount.toString(),
                    STATUS: this.sessionStatus,
                    STIME: this.startTime ||'',
                    ETIME: this.endTime,
                    USER: this.counterDes,
                    FLOATAMOUNT: this.floatAmount,
                    OPENINGAMOUNT: this.openingAmount,
                };

                this.sessionInfo = sessioInfo;

                if (item.data == 1) {
                    this.confirmActuals();
                }
            });
        }
    }

    confirmActuals() {
        const _dialogRef = this._dialog.open(ConfirmSessionActualsComponent, {
            disableClose: true,
            width: '80%',
            height: 'fit-content',
            data: { sessionInfo1: this.sessionInfo },
        });
        _dialogRef.afterClosed().subscribe((item) => {
            if (item == true) {
                this.confirmActual = true;
            }
        });

    }
}

export interface Data {
    LOGIN: string | null;
    SESSION: string | null;
    STATUS: string | null;
    STIME: string | null;
    ETIME: string | null;
    USER: string | null;
    FLOATAMOUNT: number;
    OPENINGAMOUNT: number;
}
