<div class=" bg-gray-100  overflow-hidden" ecui-height-fill>
    <div class="overflow-y-auto">
        <!-- Simple Header -->
        <div class="bg-white rounded-lg shadow-sm border p-1">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                        <mat-icon class="text-white text-lg" svgIcon="mat_outline:price_change"></mat-icon>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">Counter Operator</h1>
                        <p class="text-gray-600 text-sm">Management System</p>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- Stats -->
                    <div class="flex items-center space-x-4 bg-gray-50 rounded-lg px-4 py-2">
                        <div class="text-center">
                            <div class="text-lg font-bold text-blue-600">{{loginCount}}</div>
                            <div class="text-xs text-gray-500">Logins</div>
                        </div>
                        <div class="w-px h-6 bg-gray-300"></div>
                        <div class="text-center">
                            <div class="text-lg font-bold text-green-600">{{sessionCount}}</div>
                            <div class="text-xs text-gray-500">Sessions</div>
                        </div>
                    </div>

                    <button
                        class="flex items-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
                        (click)="print()">
                        <mat-icon class="text-white text-sm" svgIcon="mat_solid:print"></mat-icon>
                        <span class="font-medium text-sm">Print</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Counter Information -->
        <div class="bg-white rounded-lg shadow-sm border">
            <div class="p-2">
                <h2 class="text-lg font-semibold text-gray-900 mb-1">Counter Information</h2>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- Counter Details -->
                    <div class="bg-blue-50 rounded-lg p-1">
                        <div class="flex items-center justify-between">
                            <h3 class="text-sm font-medium text-gray-700">Counter Terminal</h3>
                            <mat-icon class="text-blue-600 text-lg" svgIcon="mat_outline:desktop_windows"></mat-icon>
                        </div>
                        <p class="text-lg font-bold text-blue-700">{{counterDes}}</p>
                        <p class="text-xs text-gray-500 mt-1">Terminal ID: {{counterDes}}</p>
                    </div>

                    <!-- Date -->
                    <div class="bg-green-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm font-medium text-gray-700">Current Date</h3>
                            <mat-icon class="text-green-600 text-lg" svgIcon="mat_outline:calendar_today"></mat-icon>
                        </div>
                        <p class="text-lg font-bold text-green-700">{{todayDate | date: "dd-MM-yyyy"}}</p>
                        <p class="text-xs text-gray-500 mt-1">{{todayDate | date: "EEEE"}}</p>
                    </div>

                    <!-- Status -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm font-medium text-gray-700">Counter Status</h3>
                            <mat-icon class="text-gray-600 text-lg" svgIcon="mat_outline:info"></mat-icon>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 rounded-full"
                                [ngClass]="counterStatus === 'Open' ? 'bg-green-500' : 'bg-red-500'"></div>
                            <p class="text-lg font-bold"
                                [ngClass]="counterStatus === 'Open' ? 'text-green-700' : 'text-red-700'">
                                {{counterStatus}}
                            </p>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Updated: {{todayDate | date: "HH:mm"}}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Session Information -->
        <div class="bg-white rounded-lg shadow-sm border mb-6">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">Session Management</h2>
                    <span class="bg-blue-100 text-blue-800 text-sm font-medium px-3 py-1 rounded-full">{{sessionCount}}
                        Active</span>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <!-- Login Count -->
                    <div class="bg-orange-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm font-medium text-gray-700">Login Sessions</h3>
                            <mat-icon class="text-orange-600 text-lg" svgIcon="mat_outline:login"></mat-icon>
                        </div>
                        <p class="text-xl font-bold text-orange-700">{{loginCount}}</p>
                        <p class="text-xs text-gray-500 mt-1">Active sessions</p>
                    </div>

                    <!-- Session Count -->
                    <div class="bg-blue-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm font-medium text-gray-700">Session Count</h3>
                            <mat-icon class="text-blue-600 text-lg" svgIcon="mat_outline:schedule"></mat-icon>
                        </div>
                        <p class="text-xl font-bold text-blue-700">{{sessionCount}}</p>
                        <p class="text-xs text-gray-500 mt-1">Total sessions today</p>
                    </div>

                    <!-- Session Status -->
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h3 class="text-sm font-medium text-gray-700">Session Status</h3>
                            <mat-icon class="text-gray-600 text-lg" svgIcon="mat_outline:info"></mat-icon>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 rounded-full"
                                [ngClass]="sessionStatus === 'Open' ? 'bg-green-500' : 'bg-red-500'"></div>
                            <p class="text-lg font-bold"
                                [ngClass]="sessionStatus === 'Open' ? 'text-green-700' : 'text-red-700'">
                                {{sessionStatus}}
                            </p>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Real-time status</p>
                    </div>
                </div>

                <!-- Time and Financial Information -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <!-- Start Time -->
                    <div class="bg-gray-50 rounded-lg p-3">
                        <h4 class="text-xs font-medium text-gray-600 mb-2">Start Time</h4>
                        <p class="text-sm font-bold text-gray-800">{{startTime || 'Not Started'}}</p>
                    </div>

                    <!-- End Time -->
                    <div class="bg-gray-50 rounded-lg p-3">
                        <h4 class="text-xs font-medium text-gray-600 mb-2">End Time</h4>
                        <p class="text-sm font-bold text-gray-800">{{endTime || 'Active'}}</p>
                    </div>

                    <!-- Float Amount -->
                    <div class="bg-green-50 rounded-lg p-3">
                        <h4 class="text-xs font-medium text-gray-600 mb-2">Float Amount</h4>
                        <p class="text-sm font-bold text-green-700">${{floatAmount || '0.00'}}</p>
                    </div>

                    <!-- Opening Cash -->
                    <div class="bg-green-50 rounded-lg p-3">
                        <h4 class="text-xs font-medium text-gray-600 mb-2">Opening Cash</h4>
                        <p class="text-sm font-bold text-green-700">${{openingAmount || '0.00'}}</p>
                    </div>
                </div>

                <!-- Current Operator -->
                <div class="bg-blue-50 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <mat-icon class="text-blue-600 text-xl" svgIcon="mat_outline:person"></mat-icon>
                            <div>
                                <h4 class="text-sm font-medium text-gray-700">Current Operator</h4>
                                <p class="text-lg font-bold text-blue-700">{{counterDes}}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-xs text-gray-600">Status</div>
                            <div class="text-sm font-bold text-blue-600">{{startTime ? 'Active' : 'Inactive'}}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Control Center -->
        <div class="bg-white rounded-lg shadow-sm border p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-6 text-center">Control Center</h3>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <!-- Counter Control Button -->
                <button class="flex flex-col items-center justify-center p-4 rounded-lg font-medium transition-colors"
                    [ngClass]="{
            'bg-blue-600 hover:bg-blue-700 text-white': !disableCounter,
            'bg-gray-200 text-gray-500 cursor-not-allowed': disableCounter
          }" [disabled]="disableCounter" (click)="openCounter()">
                    <mat-icon class="text-2xl mb-2" svgIcon="mat_outline:lock_open"></mat-icon>
                    <span class="text-sm font-semibold">{{counterButton}}</span>
                    <span class="text-xs opacity-75">Counter Operations</span>
                </button>

                <!-- Session Control Button -->
                <button class="flex flex-col items-center justify-center p-4 rounded-lg font-medium transition-colors"
                    [ngClass]="{
            'bg-green-600 hover:bg-green-700 text-white': !disableSession,
            'bg-gray-200 text-gray-500 cursor-not-allowed': disableSession
          }" [disabled]="disableSession" (click)="session()">
                    <mat-icon class="text-2xl mb-2" svgIcon="mat_outline:play_arrow"></mat-icon>
                    <span class="text-sm font-semibold">{{sessionButton}}</span>
                    <span class="text-xs opacity-75">Session Management</span>
                </button>

                <!-- Confirm Actuals Button -->
                <button class="flex flex-col items-center justify-center p-4 rounded-lg font-medium transition-colors"
                    [ngClass]="{
            'bg-purple-600 hover:bg-purple-700 text-white': !disableConfirmActual,
            'bg-gray-200 text-gray-500 cursor-not-allowed': disableConfirmActual
          }" [disabled]="disableConfirmActual" (click)="confirmActuals()">
                    <mat-icon class="text-2xl mb-2" svgIcon="mat_outline:check_circle"></mat-icon>
                    <span class="text-sm font-semibold">Confirm Actuals</span>
                    <span class="text-xs opacity-75">Financial Verification</span>
                </button>
            </div>

            <!-- Status Bar -->
            <div class="flex items-center justify-center space-x-6 text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span>Counter: {{counterStatus}}</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Session: {{sessionStatus}}</span>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <span>System: Online</span>
                </div>
            </div>
        </div>
    </div>
</div>